/**
 * 网络请求封装工具类
 * 基于uni.request封装，支持请求拦截、响应拦截、错误处理等功能
 */

// 请求配置接口
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  timeout?: number
  showLoading?: boolean
}

// 响应数据接口
interface ResponseData<T = any> {
  code: number
  msg: string
  data: T
}

// 请求基础配置
const BASE_CONFIG = {
  baseURL: 'https://api.watertracker.com/v1', // 接口基础地址
  timeout: 10000, // 请求超时时间
  header: {
    'Content-Type': 'application/json'
  }
}

class Request {
  private baseURL: string
  private timeout: number
  private defaultHeader: Record<string, string>

  constructor() {
    this.baseURL = BASE_CONFIG.baseURL
    this.timeout = BASE_CONFIG.timeout
    this.defaultHeader = BASE_CONFIG.header
  }

  /**
   * 获取存储的token
   */
  private getToken(): string {
    try {
      return uni.getStorageSync('token') || ''
    } catch (error) {
      console.error('获取token失败:', error)
      return ''
    }
  }

  /**
   * 设置token到本地存储
   */
  public setToken(token: string): void {
    try {
      uni.setStorageSync('token', token)
    } catch (error) {
      console.error('设置token失败:', error)
    }
  }

  /**
   * 清除token
   */
  public clearToken(): void {
    try {
      uni.removeStorageSync('token')
    } catch (error) {
      console.error('清除token失败:', error)
    }
  }

  /**
   * 请求拦截器 - 处理请求前的逻辑
   */
  private requestInterceptor(config: RequestConfig): RequestConfig {
    // 添加token到请求头
    const token = this.getToken()
    if (token) {
      config.header = {
        ...config.header,
        'Authorization': `Bearer ${token}`
      }
    }

    // 显示加载提示
    if (config.showLoading !== false) {
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
    }

    return config
  }

  /**
   * 响应拦截器 - 处理响应数据
   */
  private responseInterceptor<T>(response: any): Promise<ResponseData<T>> {
    // 隐藏加载提示
    uni.hideLoading()

    return new Promise((resolve, reject) => {
      const { statusCode, data } = response

      // HTTP状态码检查
      if (statusCode !== 200) {
        this.handleHttpError(statusCode)
        reject(new Error(`HTTP Error: ${statusCode}`))
        return
      }

      // 业务状态码检查
      if (data.code === 200) {
        resolve(data)
      } else {
        this.handleBusinessError(data.code, data.message)
        reject(new Error(data.message || '请求失败'))
      }
    })
  }

  /**
   * 处理HTTP错误
   */
  private handleHttpError(statusCode: number): void {
    let message = '网络请求失败'
    
    switch (statusCode) {
      case 400:
        message = '请求参数错误'
        break
      case 401:
        message = '登录已过期，请重新登录'
        this.clearToken()
        // 跳转到登录页面
        uni.reLaunch({
          url: '/pages/login/login'
        })
        break
      case 403:
        message = '没有权限访问'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 500:
        message = '服务器内部错误'
        break
      case 502:
        message = '网关错误'
        break
      case 503:
        message = '服务不可用'
        break
      default:
        message = `网络错误 ${statusCode}`
    }

    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 处理业务错误
   */
  private handleBusinessError(code: number, message: string): void {
    // 根据业务错误码进行不同处理
    switch (code) {
      case 401:
        // token过期或无效
        this.clearToken()
        uni.reLaunch({
          url: '/pages/login/login'
        })
        break
      default:
        uni.showToast({
          title: message || '操作失败',
          icon: 'none',
          duration: 2000
        })
    }
  }

  /**
   * 通用请求方法
   */
  private request<T = any>(config: RequestConfig): Promise<ResponseData<T>> {
    return new Promise((resolve, reject) => {
      // 处理请求配置
      const requestConfig = this.requestInterceptor({
        ...config,
        url: config.url.startsWith('http') ? config.url : this.baseURL + config.url,
        method: config.method || 'GET',
        header: {
          ...this.defaultHeader,
          ...config.header
        },
        timeout: config.timeout || this.timeout
      })

      // 发起请求
      uni.request({
        ...requestConfig,
        success: (response) => {
          this.responseInterceptor<T>(response)
            .then(resolve)
            .catch(reject)
        },
        fail: (error) => {
          uni.hideLoading()
          console.error('请求失败:', error)
          
          // 处理网络错误
          let message = '网络连接失败'
          if (error.errMsg) {
            if (error.errMsg.includes('timeout')) {
              message = '请求超时，请重试'
            } else if (error.errMsg.includes('fail')) {
              message = '网络连接失败，请检查网络'
            }
          }
          
          uni.showToast({
            title: message,
            icon: 'none',
            duration: 2000
          })
          
          reject(error)
        }
      })
    })
  }

  /**
   * GET请求
   */
  public get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    // 处理查询参数
    if (params) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&')
      url += (url.includes('?') ? '&' : '?') + queryString
    }

    return this.request<T>({
      url,
      method: 'GET',
      ...config
    })
  }

  /**
   * POST请求
   */
  public post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config
    })
  }

  /**
   * PUT请求
   */
  public put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  public delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ResponseData<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      ...config
    })
  }

  /**
   * 上传文件
   */
  public upload(url: string, filePath: string, name: string = 'file', formData?: Record<string, any>): Promise<any> {
    return new Promise((resolve, reject) => {
      const token = this.getToken()
      
      uni.showLoading({
        title: '上传中...',
        mask: true
      })

      uni.uploadFile({
        url: url.startsWith('http') ? url : this.baseURL + url,
        filePath,
        name,
        formData,
        header: token ? { 'Authorization': `Bearer ${token}` } : {},
        success: (response) => {
          uni.hideLoading()
          try {
            const data = JSON.parse(response.data)
            if (data.code === 200) {
              resolve(data)
            } else {
              this.handleBusinessError(data.code, data.message)
              reject(new Error(data.message))
            }
          } catch (error) {
            reject(new Error('响应数据解析失败'))
          }
        },
        fail: (error) => {
          uni.hideLoading()
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          })
          reject(error)
        }
      })
    })
  }
}

// 创建请求实例
const request = new Request()

// 导出请求实例和类型
export default request
export type { RequestConfig, ResponseData }
